<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scrollbar in Padding Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Amazon Ember', sans-serif;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .test-description {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        /* Add some test content to make scrolling necessary */
        .test-item {
            padding: 10px;
            margin: 5px 0;
            background: #f9f9f9;
            border-radius: 6px;
            border-left: 3px solid #606F95;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">Scrollbar in Right Padding Test</div>
        
        <div class="test-description">
            <strong>Expected behavior:</strong><br>
            • The scrollbar should appear within the 24px right padding<br>
            • Content should not be pushed or narrowed when scrollbar appears<br>
            • The card should maintain its exact 24px padding on all sides<br>
            • Scrollbar should be 6px wide and positioned in the right padding area
        </div>
        
        <!-- Today's Sales Card Test -->
        <div class="Sales-card-div">
            <div class="Sales-title-date-div">
                <span class="sales-card-title">Today's Sales</span>
                <span class="sales-card-date">Test Date</span>
            </div>
            
            <div class="sales-scrollable-content">
                <!-- Add enough content to trigger scrolling -->
                <div class="test-item">Item 1 - This content should have proper spacing</div>
                <div class="test-item">Item 2 - Check that scrollbar doesn't push content</div>
                <div class="test-item">Item 3 - Right padding should contain the scrollbar</div>
                <div class="test-item">Item 4 - Content width should remain consistent</div>
                <div class="test-item">Item 5 - Scrollbar appears on hover/scroll</div>
                <div class="test-item">Item 6 - No layout shift when scrollbar appears</div>
                <div class="test-item">Item 7 - 24px padding maintained on all sides</div>
                <div class="test-item">Item 8 - Scrollbar lives in right padding</div>
                <div class="test-item">Item 9 - Content area remains full width</div>
                <div class="test-item">Item 10 - Test scrolling behavior</div>
                <div class="test-item">Item 11 - Verify padding consistency</div>
                <div class="test-item">Item 12 - Check scrollbar positioning</div>
                <div class="test-item">Item 13 - Ensure no content clipping</div>
                <div class="test-item">Item 14 - Validate visual alignment</div>
                <div class="test-item">Item 15 - Test complete scrolling range</div>
                <div class="test-item">Item 16 - Bottom padding preserved</div>
                <div class="test-item">Item 17 - Scrollbar in padding area</div>
                <div class="test-item">Item 18 - No horizontal scrollbar</div>
                <div class="test-item">Item 19 - Smooth scrolling experience</div>
                <div class="test-item">Item 20 - Final test item</div>
            </div>
        </div>
    </div>
</body>
</html>